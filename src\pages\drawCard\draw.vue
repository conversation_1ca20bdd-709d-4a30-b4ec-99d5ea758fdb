<script setup lang="ts">
import type { IPrize, IPrizesResponse, ISpinResponse } from '@/api/turntable'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { getTurntablePrizesAPI, spinTurntableAPI } from '@/api/turntable'
import useRequest from '@/hooks/useRequest'

// 定义组件props
interface Props {
  userId?: string | null
  initialSpins?: number
}

const props = withDefaults(defineProps<Props>(), {
  userId: null,
  initialSpins: 0,
})

// 定义组件emits
const emit = defineEmits<{
  spinResult: [prize: IPrize]
  initialSpinsValue: [value: number]
}>()

// 响应式数据 - UI相关数据
const list = reactive<Array<{
  name: string
  value: string | number
  icon: string
  isNoPrize?: boolean
}>>([])

const width = ref(0)
const animationData = ref({})
const btnDisabled = ref('')
let runDeg = 0

// 转盘数据交互相关
const clickLock = ref(false)
const spinCount = ref(0)
const isInitialized = ref(false)
const spinHistory = ref<any[]>([])
const showHistory = ref(false)

// 按钮状态
const buttonText = ref('开始')
const buttonCursor = ref('pointer')
const buttonDisabled = ref(false)

// 使用useRequest管理获取奖品信息的请求状态
const {
  run: fetchPrizes,
} = useRequest<IPrizesResponse>(() => getTurntablePrizesAPI(props.userId || ''), {
  immediate: false,
})

// 使用useRequest管理抽奖请求状态
const {
  run: executeSpin,
} = useRequest<ISpinResponse>(() => spinTurntableAPI(props.userId || ''), {
  immediate: false,
})

// 默认奖品数据（作为备用）
const defaultPrizes: IPrize[] = [
  { name: '5折优惠', value: 5 },
  { name: '6折优惠', value: 6 },
  { name: '7折优惠', value: 7 },
  { name: '8折优惠', value: 8 },
  { name: '9折优惠', value: 9 },
  { name: '感谢参与', value: 0 },
]

// 将API奖品数据转换为UI显示格式
function convertPrizesToList(prizes: IPrize[]) {
  return prizes.map((prize, index) => ({
    name: prize.name,
    value: prize.value.toString(),
    icon: getIconForPrize(prize, index),
    isNoPrize: prize.value === 0 || prize.name.includes('感谢') || prize.name.includes('谢谢'),
  }))
}

// 根据奖品信息获取对应的图标
function getIconForPrize(prize: IPrize, index: number) {
  // 如果是无奖项
  if (prize.value === 0 || prize.name.includes('感谢') || prize.name.includes('谢谢')) {
    return 'iconfangqi1 text-master'
  }

  // 根据奖品名称或索引分配图标
  if (prize.name.includes('折') || prize.name.includes('优惠')) {
    return 'icondazhe text-danger'
  }

  // 默认图标
  return 'icondazhe text-danger'
}

// 初始化转盘
async function initializeTurntable() {
  if (isInitialized.value)
    return
  isInitialized.value = true

  try {
    // 检查是否有userId
    if (!props.userId) {
      buttonText.value = '转盘异常'
      buttonCursor.value = 'not-allowed'
      buttonDisabled.value = true
      return
    }

    // 使用useRequest获取奖品数据
    const data = await fetchPrizes()
    console.log(data)

    if (data) {
      // 将API奖品数据转换为UI显示格式
      const convertedPrizes = convertPrizesToList(data.prizes)
      list.splice(0, list.length, ...convertedPrizes)
      spinCount.value = data.remainingSpins
      updateWidth()

      if (data.spinResults && data.spinResults.length > 0) {
        spinHistory.value = []
        let totalPrizeValueFromHistory = 0

        data.spinResults.forEach((result) => {
          totalPrizeValueFromHistory += result.value || 0
          const prizeTime = new Date(result.timestamp).toLocaleString()
          spinHistory.value.push({
            name: result.name,
            time: prizeTime,
          })
        })

        if (totalPrizeValueFromHistory > 0) {
          emit('initialSpinsValue', totalPrizeValueFromHistory)
        }
        showHistory.value = true
      }

      updateButtonText()
    }
    else {
      // API返回空数据，使用默认数据
      console.warn('API返回空数据，使用默认奖品数据')
      const convertedPrizes = convertPrizesToList(defaultPrizes)
      list.splice(0, list.length, ...convertedPrizes)
      spinCount.value = 0
      updateWidth()
      updateButtonText()
    }
  }
  catch (error) {
    console.error('Failed to initialize turntable prizes:', error)
    // API返回错误，使用默认数据
    console.warn('API请求失败，使用默认奖品数据')
    const convertedPrizes = convertPrizesToList(defaultPrizes)
    list.splice(0, list.length, ...convertedPrizes)
    spinCount.value = 0
    updateWidth()
    updateButtonText()
  }
}

// 更新按钮文本
function updateButtonText() {
  if (spinCount.value > 0) {
    buttonText.value = `开始 (${spinCount.value})`
    buttonCursor.value = 'pointer'
    buttonDisabled.value = false
    btnDisabled.value = ''
  }
  else {
    buttonText.value = '无抽奖次数'
    buttonCursor.value = 'not-allowed'
    buttonDisabled.value = true
    btnDisabled.value = 'disabled'
  }
}

// 抽奖点击处理
async function handleSpin() {
  if (clickLock.value || spinCount.value <= 0 || buttonDisabled.value) {
    return
  }
  clickLock.value = true
  buttonDisabled.value = true
  btnDisabled.value = 'disabled'

  try {
    // 检查是否有userId
    if (!props.userId) {
      buttonText.value = '转盘异常'
      buttonCursor.value = 'not-allowed'
      buttonDisabled.value = true
      btnDisabled.value = 'disabled'
      clickLock.value = false
      return
    }

    // 使用useRequest执行抽奖
    const data = await executeSpin()
    if (data) {
      const prize = data.prize
      const prizeIndex = data.prizeIndex
      spinCount.value = data.remainingSpins

      updateButtonText()

      // 执行转盘动画，使用prizeIndex作为中奖位置
      animation(prizeIndex, 4000, prize)

      // 添加到历史记录
      const prizeTime = new Date().toLocaleString()
      spinHistory.value.unshift({
        name: prize.name,
        time: prizeTime,
      })
      showHistory.value = true
    }
    else {
      throw new Error('抽奖API返回空数据')
    }
  }
  catch (error) {
    console.error('Spin failed:', error)
    // API返回错误，显示转盘异常
    buttonText.value = '转盘异常'
    buttonCursor.value = 'not-allowed'
    buttonDisabled.value = true
    btnDisabled.value = 'disabled'
    clickLock.value = false
  }
}

// 修改原有的animation函数，支持传入奖品信息
function animation(index: number, duration: number, prize?: IPrize) {
  const runNum = 4 // 旋转4周

  // 旋转角度
  runDeg = runDeg || 0
  runDeg = runDeg + (360 - runDeg % 360) + (360 * runNum - index * (360 / list.length)) + 1

  // 创建动画
  const animationRun = uni.createAnimation({
    duration,
    timingFunction: 'ease',
  })
  animationRun.rotate(runDeg).step()
  animationData.value = animationRun.export()

  setTimeout(() => {
    if (prize) {
      // 如果有奖品信息，显示中奖结果
      uni.showModal({
        content: `恭喜您抽中：${prize.name}`,
        showCancel: false,
      })
      emit('spinResult', prize)
    }
    else {
      // 原有的逻辑保持不变
      uni.showModal({ content: list[index].isNoPrize ? '抱歉，您未中奖' : '恭喜，中奖' })
    }

    clickLock.value = false
    updateButtonText()
  }, duration + 1000)
}

// 兼容原有的playReward函数名
const playReward = handleSpin

// 监听props变化，当initialSpins改变时更新spinCount
watch(() => props.initialSpins, (newVal) => {
  if (newVal > 0) {
    spinCount.value = newVal
    updateButtonText()
  }
})

// 更新转盘宽度
function updateWidth() {
  if (list.length > 0) {
    width.value = 360 / list.length
  }
  else {
    // 默认6个奖品
    width.value = 360 / 6
  }
}

// 生命周期钩子
onMounted(() => {
  // 初始化转盘数据
  nextTick(async () => {
    await initializeTurntable()
    // 初始化完成后更新宽度
    updateWidth()
  })
})
</script>

<template>
  <view class="conbox">
    <view class="container">
      <!-- 背景 -->
      <!-- <image src="../../static/images/bg.png" class="cont" mode=""></image> -->
      <!-- <image src="../../static/images/caidai.png" class="caidai" mode=""></image> -->
      <view class="main" style="padding-top: 50upx;">
        <view class="canvas-container">
          <view id="zhuanpano" :animation="animationData" class="canvas-content" style="">
            <view class="canvas-line">
              <view
                v-for="(_, index) in list" :key="index" class="canvas-litem"
                :style="{ transform: `rotate(${index * width + width / 2}deg)` }"
              />
            </view>

            <view class="canvas-list">
              <view v-for="(iteml, index) in list" :key="index" class="canvas-item" :style="{ transform: `rotate(${index * width}deg)`, zIndex: index }">
                <view class="canvas-item-text" :style="`transform:rotate(${index})`">
                  <text class="b" style="font-size: 32upx;">
                    {{ iteml.name }}
                  </text>
                  <text class="icon-awrad iconfont" :class="iteml.icon" />
                </view>
              </view>
            </view>
          </view>

          <view
            class="canvas-btn"
            :class="btnDisabled"
            :style="{ cursor: buttonCursor }"
            @tap="playReward"
          >
            {{ buttonText }}
          </view>
        </view>
      </view>

      <!-- 抽奖历史记录 -->
      <view v-if="showHistory" class="spin-history">
        <text class="history-title">
          抽奖记录
        </text>
        <scroll-view class="history-list" scroll-y>
          <view
            v-for="(record, index) in spinHistory"
            :key="index"
            class="history-item"
          >
            抽中: {{ record.name }} - {{ record.time }}
          </view>
        </scroll-view>
      </view>

      <!-- 规则 -->
      <!-- <view class="guize" style="margin-top: 100upx;">
        <view class="title">
          规则说明
        </view>
        <view class="g_item">
          1.用户每天登录即送1次抽奖机会，分享好友则多赠1次机会
        </view>
        <view class="g_item">
          2.用户点击大转盘抽奖按钮，有积分和现金两种方式可参与抽奖，没抽一次消耗1次抽奖机会
        </view>
        <view class="g_item">
          3.用户获得的奖品，可在我的道具里查看
        </view>
      </view> -->
    </view>
  </view>
</template>

<style scoped>
.icon-awrad {
  font-size: 50upx !important;
}

.conbox {
  width: 750upx;
  height: 100vh;
  overflow-x: hidden;
  overflow-y: scroll;
}

.container,
image.cont {
  width: 750upx;
  min-height: 100vh;
  height: auto;
  position: relative;
}

image.cont {
  height: 100%;
  position: absolute;
  z-index: 0;
}

image.caidai {
  position: absolute;
  top: 0;
  left: 0;
  width: 750upx;
  height: 1024upx;
}

.header-title > view {
  padding: 8upx 16upx;
  border: 1px solid #d89720;
  color: #d89720;
  font-size: 28upx;
  border-radius: 26upx;
}

/* 转盘 */
.canvas-container {
  margin: 0 auto;
  position: relative;
  width: 600upx;
  height: 600upx;
  background: url(./circle.png) no-repeat;
  background-size: cover;
  border-radius: 50%;
}

.canvas-content {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  display: block;
  width: 600upx;
  height: 600upx;
  border-radius: inherit;
  /* background-clip: padding-box; */
  /* background-color: #ffcb3f; */
}

.canvas-list {
  position: absolute;
  left: 0;
  top: 0;
  width: inherit;
  height: inherit;
  z-index: 9999;
}

.canvas-item {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  color: #e4370e;
  /* text-shadow: 0 1upx 1upx rgba(255, 255, 255, 0.6); */
}

.canvas-item-text {
  position: relative;
  display: block;
  padding-top: 46upx;
  margin: 0 auto;
  text-align: center;
  -webkit-transform-origin: 50% 300upx;
  transform-origin: 50% 300upx;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fb778b;
}

.canvas-item-text text {
  font-size: 30upx;
}

/* 分隔线 */
.canvas-line {
  position: absolute;
  left: 0;
  top: 0;
  width: inherit;
  height: inherit;
  z-index: 99;
}

.canvas-litem {
  position: absolute;
  left: 300upx;
  top: 0;
  width: 3upx;
  height: 300upx;
  background-color: rgba(228, 55, 14, 0.4);
  overflow: hidden;
  -webkit-transform-origin: 50% 300upx;
  transform-origin: 50% 300upx;
}

/**
* 抽奖按钮
*/
.canvas-btn {
  position: absolute;
  left: 260upx;
  top: 260upx;
  z-index: 400;
  width: 80upx;
  height: 80upx;
  border-radius: 50%;
  color: #f4e9cc;
  background-color: #e44025;
  line-height: 80upx;
  text-align: center;
  font-size: 26upx;
  text-shadow: 0 -1px 1px rgba(0, 0, 0, 0.6);
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.6);
  text-decoration: none;
}

.canvas-btn::after {
  position: absolute;
  display: block;
  content: ' ';
  left: 12upx;
  top: -44upx;
  width: 0;
  height: 0;
  overflow: hidden;
  border-width: 30upx;
  border-style: solid;
  border-color: transparent;
  border-bottom-color: #e44025;
}
.canvas-btn.disabled {
  pointer-events: none;
  background: #b07a7b;
  color: #ccc;
}

.canvas-btn.disabled::after {
  border-bottom-color: #b07a7b;
}

.typecheckbox view {
  border: 1px solid #ff3637;
  background: transparent;
  color: #ff3637;
  display: flex;
  height: 60upx;
  width: 140upx;
  border-radius: 50upx;
  align-items: center;
  justify-content: center;
  display: flex;
  margin-left: 20upx;
}

.guize {
  width: 502upx;
  min-height: 300upx;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 3;
  background-image: linear-gradient(-180deg, #f48549 0%, #f2642e 100%);
  border: 18upx solid #e4431a;
  border-radius: 16px;
  margin: 0 auto;
  margin-top: -104upx;
  padding: 48upx;
  /* box-sizing: border-box; */
  color: #fff;
}

.guize .title {
  text-align: center;
  margin-bottom: 28upx;
}

.guize .g_item {
  font-family: PingFang-SC-Medium;
  font-size: 24upx;
  color: #ffffff;
  letter-spacing: 0.5px;
  text-align: justify;
  line-height: 20px;
}

.myrewards .title {
  font-family: PingFang-SC-Bold;
  font-size: 16px;
  color: #e4431a;
  letter-spacing: 0.57px;
  display: flex;
  padding-top: 36upx;
  justify-content: center;
}

/* 抽奖历史记录样式 */
.spin-history {
  margin-top: 40upx;
  padding: 20upx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10upx;
  max-width: 600upx;
  margin-left: auto;
  margin-right: auto;
}

.history-title {
  display: block;
  text-align: center;
  font-size: 32upx;
  color: #fff;
  margin-bottom: 20upx;
  font-weight: bold;
}

.history-list {
  max-height: 300upx;
}

.history-item {
  padding: 10upx 0;
  color: #fff;
  font-size: 28upx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.history-item:last-child {
  border-bottom: none;
}
</style>
